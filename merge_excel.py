#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel文件合并脚本
合并四个季节的丰度表，按照指定规则处理数据
"""

import pandas as pd
import numpy as np
from pathlib import Path


def load_seasonal_data():
    """加载四个季节的数据文件"""
    files = {
        "Sp": "春季丰度表传统调查.xlsx",
        "Su": "夏季丰度表传统调查.xlsx",
        "Au": "秋季丰度表传统调查.xlsx",
        "Wi": "冬季丰度表传统调查.xlsx",
    }

    seasonal_data = {}
    for season, filename in files.items():
        if Path(filename).exists():
            df = pd.read_excel(filename)
            seasonal_data[season] = df
            print(f"✓ 加载 {season}季数据: {len(df)} 物种, {len(df.columns)} 列")
        else:
            print(f"✗ 文件不存在: {filename}")

    return seasonal_data


def get_all_data_columns(seasonal_data):
    """获取所有季节数据列的并集"""
    exclude_cols = ["species", "目", "科", "属", "种", "种类"]

    # 获取每个季节的数据列
    season_columns = {}
    all_columns = set()

    for season, df in seasonal_data.items():
        data_cols = [col for col in df.columns if col not in exclude_cols]
        season_columns[season] = set(data_cols)
        all_columns.update(data_cols)
        print(f"{season}季数据列数量: {len(data_cols)}")

    # 所有季节数据列的并集
    all_columns = sorted(list(all_columns))

    print(f"\n所有季节数据列并集数量: {len(all_columns)}")
    print(f"所有数据列: {all_columns}")

    return all_columns, season_columns


def get_all_species_info(seasonal_data):
    """获取所有物种的完整信息（并集）"""
    all_species = {}
    taxonomy_cols = ["目", "科", "属", "种", "种类"]

    for season, df in seasonal_data.items():
        for _, row in df.iterrows():
            species = row["species"]
            if species not in all_species:
                # 创建物种信息字典
                species_info = {"species": species}
                for col in taxonomy_cols:
                    if col in df.columns:
                        species_info[col] = row[col] if pd.notna(row[col]) else ""
                    else:
                        species_info[col] = ""
                all_species[species] = species_info
            else:
                # 更新分类学信息（用非空值填充空值）
                for col in taxonomy_cols:
                    if col in df.columns and pd.notna(row[col]) and row[col] != "":
                        if all_species[species][col] == "":
                            all_species[species][col] = row[col]

    print(f"\n物种总数（并集）: {len(all_species)}")
    return all_species


def create_merged_dataframe(seasonal_data, all_columns, season_columns, all_species):
    """创建合并后的数据框"""
    # 创建基础数据框，包含所有物种
    species_list = list(all_species.keys())
    merged_df = pd.DataFrame({"species": species_list})

    # 添加分类学信息
    taxonomy_cols = ["目", "科", "属", "种", "种类"]
    for col in taxonomy_cols:
        merged_df[col] = [all_species[species][col] for species in species_list]

    # 为每个季节添加数据列
    for season in ["Sp", "Su", "Au", "Wi"]:
        if season in seasonal_data:
            season_df = seasonal_data[season]

            # 为当前季节的每个数据列添加数据（包括该季节没有的列）
            for col in all_columns:
                new_col_name = f"{season}|{col}"

                # 创建一个字典，用于快速查找物种数据
                species_data = {}
                if col in season_columns[season]:  # 该季节有这个列
                    for _, row in season_df.iterrows():
                        species_data[row["species"]] = (
                            row[col] if pd.notna(row[col]) else 0
                        )
                # 如果该季节没有这个列，species_data保持为空，所有物种都会填充0

                # 为所有物种填充数据（存在则用实际值，不存在则用0）
                merged_df[new_col_name] = [
                    species_data.get(species, 0) for species in species_list
                ]

    return merged_df


def main():
    """主函数"""
    print("开始合并Excel文件...")
    print("=" * 50)

    # 1. 加载数据
    seasonal_data = load_seasonal_data()
    if not seasonal_data:
        print("错误：没有找到任何数据文件！")
        return

    # 2. 获取所有数据列
    all_columns, season_columns = get_all_data_columns(seasonal_data)
    if not all_columns:
        print("错误：没有找到任何数据列！")
        return

    # 3. 获取所有物种信息
    all_species = get_all_species_info(seasonal_data)

    # 4. 创建合并数据框
    print("\n开始合并数据...")
    merged_df = create_merged_dataframe(
        seasonal_data, all_columns, season_columns, all_species
    )

    # 5. 保存结果
    output_file = "合并结果.xlsx"
    merged_df.to_excel(output_file, index=False)

    print(f"\n✓ 合并完成！")
    print(f"输出文件: {output_file}")
    print(f"最终数据规模: {len(merged_df)} 行 × {len(merged_df.columns)} 列")
    print(f"数据列结构: species + {len(all_columns)*4} 个季节数据列 + 5 个分类学列")


if __name__ == "__main__":
    main()
